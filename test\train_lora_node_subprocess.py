import subprocess
import sys
import os
import argparse
from pathlib import Path
import threading

# # 添加 sd_scripts 和 library 到 sys.path
# CURRENT = Path(__file__).resolve().parent
# sys.path.insert(0, str(CURRENT))

# for path in ["sd_scripts", "sd_scripts/library"]:
#     sys.path.insert(0, str(CURRENT / path))
for p in sys.path:
    print(f"===================sys.path: {p}")

def parse_args():
    """解析命令行参数"""
    parser = argparse.ArgumentParser()
    parser.add_argument("--pretrained_model_name_or_path", type=str, required=True)
    parser.add_argument("--train_data_dir", type=str, required=True)
    parser.add_argument("--output_dir", type=str, required=True)
    parser.add_argument("--output_name", type=str, default="my_lora")
    parser.add_argument("--network_dim", type=int, default=32)
    parser.add_argument("--network_alpha", type=int, default=32)
    parser.add_argument("--batch_size", type=int, default=1)
    parser.add_argument("--learning_rate", type=float, default=0.0001)
    parser.add_argument("--max_train_steps", type=int, default=1000)
    parser.add_argument("--save_model_as", type=str, default="safetensors")
    parser.add_argument("--mixed_precision", type=str, default="fp16")
    parser.add_argument("--save_every_n_steps", type=int, default=500)
    parser.add_argument("--seed", type=int, default=42)
    return parser.parse_args()

def run_lora_training(params):
    """通过子进程运行LoRA训练"""
    try:
        print("===== 开始LoRA训练 =====")
        print(f"参数: {params}")
        
        # 设置路径
        python_exe = sys.executable
        script_path = Path(__file__).parent / "sd_scripts" / "train_network.py"
        print(f"训练脚本路径: {script_path}")
        
        # 创建隔离的环境变量
        child_env = os.environ.copy()
        base_path = Path(__file__).parent
        # 设置 PYTHONPATH 仅为 sd_scripts
        child_env["PYTHONPATH"] = str(base_path / "sd_scripts")
        child_env.pop("PYTHONHOME", None)
        
        # 构造参数列表
        args = [python_exe, str(script_path)]
        for key, value in params.items():
            if value is not None:
                args.extend([f"--{key}", str(value)])
        
        # 打印完整命令和环境变量
        print("执行命令:", " ".join(args))
        print("环境变量PYTHONPATH:", child_env.get("PYTHONPATH", ""))
        
        # 启动子进程并捕获输出
        proc = subprocess.Popen(
            args,
            env=child_env,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True,
            cwd=str(base_path),  # 设置工作目录
            creationflags=subprocess.CREATE_NO_WINDOW if sys.platform == "win32" else 0
        )
        
        # 实时处理输出
        def handle_output(stream, prefix):
            for line in stream:
                print(f"[LORA TRAIN {prefix}] {line}", end="")
        
        stdout_thread = threading.Thread(
            target=handle_output, args=(proc.stdout, "STDOUT")
        )
        stderr_thread = threading.Thread(
            target=handle_output, args=(proc.stderr, "STDERR")
        )
        stdout_thread.start()
        stderr_thread.start()
        
        proc.wait()
        stdout_thread.join()
        stderr_thread.join()
        
        if proc.returncode != 0:
            raise RuntimeError(f"Training failed with exit code {proc.returncode}")
            
        return True
    except Exception as e:
        print(f"[ERROR] LORA training failed: {str(e)}")
        return False

if __name__ == "__main__":
    args = parse_args()
    params = {
        "pretrained_model_name_or_path": args.pretrained_model_name_or_path,
        "train_data_dir": args.train_data_dir,
        "output_dir": args.output_dir,
        "output_name": args.output_name,
        "network_dim": args.network_dim,
        "network_alpha": args.network_alpha,
        "batch_size": args.batch_size,
        "learning_rate": args.learning_rate,
        "max_train_steps": args.max_train_steps,
        "save_model_as": args.save_model_as,
        "mixed_precision": args.mixed_precision,
        "save_every_n_steps": args.save_every_n_steps,
        "seed": args.seed
    }
    run_lora_training(params)
