import os
from pathlib import Path
from folder_paths import models_dir
from .sd_scripts.train_network import NetworkTrainer, setup_parser

def get_model_list():
    """获取 ComfyUI 模型目录中的模型列表"""
    model_extensions = ['.safetensors', '.ckpt', '.pt', '.pth', '.bin']
    model_list = []
    
    # 支持多个常见模型目录
    model_dirs = [
        os.path.join(models_dir, "checkpoints"),
        os.path.join(models_dir, "stable-diffusion"), 
        os.path.join(models_dir, "models"),
        os.path.join(models_dir, "net")
    ]
    
    for dir_path in model_dirs:
        if os.path.exists(dir_path):
            for file in os.listdir(dir_path):
                if any(file.lower().endswith(ext) for ext in model_extensions):
                    model_list.append(os.path.join(dir_path, file))
    
    # 去重并排序
    model_list = list(set(model_list))
    model_list.sort(key=lambda x: os.path.basename(x).lower())
    return model_list

class TrainLoRANode:
    @classmethod
    def INPUT_TYPES(cls):
        return {
            "required": {
                "pretrained_model": (get_model_list(), {"default": ""}),
                "train_data_dir": ("STRING", {"default": ""}),
                "output_dir": ("STRING", {"default": ""}),
                "output_name": ("STRING", {"default": "my_lora"}),
                "resolution": ("INT", {
                    "default": 512, "min": 256, "max": 1024, "step": 64,
                    "description": "训练分辨率，建议512或768"
                }),
                "network_dim": ("INT", {
                    "default": 32, "min": 1, "max": 256,
                    "description": "网络维度（秩），越大越精细但更耗显存"
                }),
                "network_alpha": ("INT", {
                    "default": 32, "min": 1, "max": 256,
                    "description": "网络 alpha 通常与 dim 相等或一半"
                }),
                "learning_rate": ("FLOAT", {
                    "default": 1e-4, "min": 1e-6, "max": 1e-2,
                    "description": "建议范围 1e-5 ~ 5e-4"
                }),
                "batch_size": ("INT", {
                    "default": 1, "min": 1, "max": 4,
                    "description": "批大小，根据显存调整"
                }),
                "max_train_steps": ("INT", {
                    "default": 1000, "min": 100, "max": 100000,
                    "description": "最大训练步数"
                }),
                "save_every_n_steps": ("INT", {
                    "default": 500, "min": 100, "max": 10000,
                    "description": "每隔多少步保存一次模型"
                }),
                "save_model_as": (["safetensors", "ckpt", "pt"], {
                    "default": "safetensors",
                    "description": "保存模型的格式"
                }),
                "mixed_precision": (["fp16", "bf16", "no"], {
                    "default": "fp16",
                    "description": "是否启用混合精度"
                }),
                "seed": ("INT", {
                    "default": 42, "min": 0, "max": 2**32 - 1,
                    "description": "随机种子"
                }),
                "network_module": (["networks.lora", "networks.dylora", "networks.locon"], {
                    "default": "networks.lora",
                    "description": "网络模块类型"
                }),
                "random_crop": (["true", "false"], {
                    "default": "false",
                    "description": "是否启用随机裁剪"
                }),
            }
        }

    RETURN_TYPES = ("STRING",)
    RETURN_NAMES = ("lora_path",)
    FUNCTION = "train"
    CATEGORY = "training"

    def train(self, pretrained_model, train_data_dir, output_dir, output_name,
              resolution, network_dim, network_alpha, learning_rate, batch_size,
              max_train_steps, save_every_n_steps, save_model_as="safetensors",
              mixed_precision="fp16", seed=42, network_module="networks.lora",
              random_crop="false"):

        parser = setup_parser()
        args = parser.parse_args([])  # 获取默认参数

        # 覆盖为用户输入参数
        args.pretrained_model_name_or_path = pretrained_model
        args.train_data_dir = train_data_dir
        args.output_dir = output_dir
        args.output_name = output_name
        args.resolution = f"{resolution},{resolution}"
        args.network_dim = network_dim
        args.network_alpha = network_alpha
        args.learning_rate = learning_rate
        args.train_batch_size = batch_size
        args.max_train_steps = max_train_steps
        args.save_every_n_steps = save_every_n_steps
        args.save_model_as = save_model_as
        args.mixed_precision = mixed_precision
        args.seed = seed
        args.network_module = network_module
        args.random_crop = (random_crop.lower() == "true")

        try:
            trainer = NetworkTrainer()
            trainer.train(args)
            print("训练完成!")
        except Exception as e:
            print(f"训练出错: {e}")
            return ("",)

        # 构造模型路径
        lora_filename = f"{output_name}.{save_model_as}"
        lora_path = os.path.join(output_dir, lora_filename)
        return (lora_path,)

# 注册为 ComfyUI 节点
NODE_CLASS_MAPPINGS = {
    "TrainLoRANode": TrainLoRANode
}

NODE_DISPLAY_NAME_MAPPINGS = {
    "TrainLoRANode": "训练LoRA模型"
}
