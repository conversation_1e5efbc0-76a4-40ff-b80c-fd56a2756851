@echo off
chcp 65001
setlocal enabledelayedexpansion

echo ========================================
echo           LoRA 训练脚本
echo ========================================
echo.

:: 设置Python路径
set PYTHON_PATH=E:\AI\python\python.exe

:: 设置脚本路径
set SCRIPT_PATH=%~dp0training.py
echo 正在调用脚本: %SCRIPT_PATH%

:: 执行模式设置
:: thread - 线程模式 (默认): 在当前进程中使用线程执行训练，资源共享，速度较快
:: subprocess - 子进程模式: 创建独立子进程执行训练，资源隔离，更稳定
set EXECUTION_MODE=thread
set MODEL_PATH=E:\AI\ComfyUI\models\checkpoints\v1-5-pruned-emaonly-fp16.safetensors
set DATA_DIR=E:\training_data\images
set OUTPUT_DIR=E:\training_data\LoraOutput

:: 验证训练数据目录
if not exist "%DATA_DIR%" (
    echo 错误: 训练数据目录不存在: %DATA_DIR%
    pause
    exit /b 1
)


set OUTPUT_NAME=my_lora
set NETWORK_DIM=32
set NETWORK_ALPHA=32
set BATCH_SIZE=1
set LR=0.0001
set STEPS=10
set RESOLUTION=512

:: 设置 PYTHONPATH
set PYTHONPATH=E:\AI\ComfyUI\custom_nodes\comfyui_lora_trainer;E:\AI\ComfyUI\custom_nodes\comfyui_lora_trainer\sd_scripts;%PYTHONPATH%
echo PYTHONPATH设置为: %PYTHONPATH%

echo 执行模式: %EXECUTION_MODE%
echo 可选模式: thread (线程模式) 或 subprocess (子进程模式)
echo.

:: 执行命令
%PYTHON_PATH% "%SCRIPT_PATH%" ^
    --execution_mode %EXECUTION_MODE% ^
    --pretrained_model_name_or_path "%MODEL_PATH%" ^
    --train_data_dir "%DATA_DIR%" ^
    --output_dir "%OUTPUT_DIR%" ^
    --output_name "%OUTPUT_NAME%" ^
    --network_dim %NETWORK_DIM% ^
    --network_alpha %NETWORK_ALPHA% ^
    --train_batch_size %BATCH_SIZE% ^
    --learning_rate %LR% ^
    --max_train_steps %STEPS% ^
    --save_model_as safetensors ^
    --mixed_precision fp16 ^
    --save_every_n_steps 500 ^
    --seed 42 ^
    --resolution %RESOLUTION%,%RESOLUTION% ^
    --caption_extension .txt ^
    --network_module networks.lora ^
    --bucket_reso_steps=64 ^
    --min_bucket_reso=320 ^
    --max_bucket_reso=1280 ^
    --enable_bucket ^
    --logging_dir "%OUTPUT_DIR%\logs" ^
    --cache_latents ^
    --xformers ^
    --gradient_checkpointing ^
    --cache_latents_to_disk ^
    --gradient_accumulation_steps 1



if %ERRORLEVEL% neq 0 (
    echo 训练失败，错误代码: %ERRORLEVEL%
    pause
    exit /b %ERRORLEVEL%
)

echo 训练完成!
pause
