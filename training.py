import subprocess
import sys
import os
import argparse
from pathlib import Path
import threading


# 添加 sd_scripts 和 library 到 sys.path

CURRENT = Path(__file__).resolve().parent
sys.path.insert(0, str(CURRENT))

for path in ["sd_scripts",]:
    sys.path.insert(0, str(CURRENT / path))


# for p in sys.path:
#     print(f"===================sys.path: {p}")


from sd_scripts.train_network import NetworkTrainer, setup_parser

if __name__ == "__main__":
    parser = setup_parser()
    args = parser.parse_args()
    trainer = NetworkTrainer()
    trainer.train(args)
