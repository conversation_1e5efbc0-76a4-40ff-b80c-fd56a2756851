import subprocess
import sys
import os
import argparse
from pathlib import Path
import threading
import time
import queue
import signal


# 添加 sd_scripts 和 library 到 sys.path
CURRENT = Path(__file__).resolve().parent
sys.path.insert(0, str(CURRENT))

for path in ["sd_scripts",]:
    sys.path.insert(0, str(CURRENT / path))

from sd_scripts.train_network import NetworkTrainer, setup_parser


class TrainingExecutor:
    """训练执行器，支持线程和子进程两种模式"""

    def __init__(self, execution_mode="thread"):
        self.execution_mode = execution_mode
        self.is_running = False
        self.stop_event = threading.Event()
        self.result_queue = queue.Queue()
        self.training_thread = None
        self.subprocess_proc = None

    def execute_training_thread(self, args):
        """在线程中执行训练"""
        try:
            print(f"[线程模式] 开始LoRA训练...")
            print(f"[线程模式] 模型: {args.pretrained_model_name_or_path}")
            print(f"[线程模式] 数据目录: {args.train_data_dir}")
            print(f"[线程模式] 输出目录: {args.output_dir}")

            trainer = NetworkTrainer()
            trainer.train(args)

            if not self.stop_event.is_set():
                self.result_queue.put(("success", "训练完成"))
                print(f"[线程模式] 训练成功完成!")
            else:
                self.result_queue.put(("stopped", "训练被中断"))
                print(f"[线程模式] 训练被用户中断")

        except Exception as e:
            print(f"[线程模式] 训练出错: {e}")
            self.result_queue.put(("error", str(e)))
        finally:
            self.is_running = False

    def execute_training_subprocess(self, args):
        """在子进程中执行训练"""
        try:
            print(f"[子进程模式] 开始LoRA训练...")
            print(f"[子进程模式] 模型: {args.pretrained_model_name_or_path}")
            print(f"[子进程模式] 数据目录: {args.train_data_dir}")
            print(f"[子进程模式] 输出目录: {args.output_dir}")

            # 准备子进程参数
            python_exe = sys.executable
            script_path = CURRENT / "sd_scripts" / "train_network.py"

            # 创建隔离的环境变量
            child_env = os.environ.copy()
            child_env["PYTHONPATH"] = str(CURRENT / "sd_scripts")
            child_env.pop("PYTHONHOME", None)

            # 构造命令行参数
            cmd_args = [python_exe, str(script_path)]

            # 将 args 对象转换为命令行参数
            args_dict = vars(args)
            for key, value in args_dict.items():
                if value is not None and key != "execution_mode":
                    if isinstance(value, bool):
                        if value:
                            cmd_args.append(f"--{key}")
                    else:
                        cmd_args.extend([f"--{key}", str(value)])

            print(f"[子进程模式] 执行命令: {python_exe} {script_path} ...")

            # 启动子进程
            self.subprocess_proc = subprocess.Popen(
                cmd_args,
                env=child_env,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True,
                cwd=str(CURRENT),
                creationflags=subprocess.CREATE_NO_WINDOW if sys.platform == "win32" else 0
            )

            # 实时处理输出
            def handle_output(stream, prefix):
                try:
                    for line in stream:
                        if not self.stop_event.is_set():
                            print(f"[子进程 {prefix}] {line.strip()}")
                        else:
                            break
                except Exception as e:
                    print(f"[子进程 {prefix}] 输出处理错误: {e}")

            stdout_thread = threading.Thread(target=handle_output, args=(self.subprocess_proc.stdout, "STDOUT"))
            stderr_thread = threading.Thread(target=handle_output, args=(self.subprocess_proc.stderr, "STDERR"))

            stdout_thread.daemon = True
            stderr_thread.daemon = True
            stdout_thread.start()
            stderr_thread.start()

            # 等待进程完成或停止信号
            while self.subprocess_proc.poll() is None:
                if self.stop_event.is_set():
                    print(f"[子进程模式] 收到停止信号，正在终止子进程...")
                    self.subprocess_proc.terminate()
                    time.sleep(2)
                    if self.subprocess_proc.poll() is None:
                        self.subprocess_proc.kill()
                    break
                time.sleep(0.5)

            # 等待输出线程结束
            stdout_thread.join(timeout=2)
            stderr_thread.join(timeout=2)

            return_code = self.subprocess_proc.returncode
            if return_code == 0:
                self.result_queue.put(("success", "训练完成"))
                print(f"[子进程模式] 训练成功完成!")
            elif self.stop_event.is_set():
                self.result_queue.put(("stopped", "训练被中断"))
                print(f"[子进程模式] 训练被用户中断")
            else:
                self.result_queue.put(("error", f"子进程退出码: {return_code}"))
                print(f"[子进程模式] 训练失败，退出码: {return_code}")

        except Exception as e:
            print(f"[子进程模式] 训练出错: {e}")
            self.result_queue.put(("error", str(e)))
        finally:
            self.is_running = False
            self.subprocess_proc = None

    def start_training(self, args):
        """启动训练"""
        if self.is_running:
            print("训练已在进行中，请等待当前训练完成")
            return False

        self.is_running = True
        self.stop_event.clear()

        print(f"=== 开始训练 (执行模式: {self.execution_mode}) ===")

        if self.execution_mode == "thread":
            self.training_thread = threading.Thread(target=self.execute_training_thread, args=(args,))
            self.training_thread.daemon = True
            self.training_thread.start()
        elif self.execution_mode == "subprocess":
            self.training_thread = threading.Thread(target=self.execute_training_subprocess, args=(args,))
            self.training_thread.daemon = True
            self.training_thread.start()
        else:
            self.is_running = False
            print(f"错误: 不支持的执行模式 '{self.execution_mode}'")
            return False

        return True

    def stop_training(self):
        """停止训练"""
        if not self.is_running:
            print("没有正在进行的训练")
            return False

        print("正在停止训练...")
        self.stop_event.set()

        # 如果是子进程模式，直接终止子进程
        if self.subprocess_proc:
            try:
                self.subprocess_proc.terminate()
                time.sleep(2)
                if self.subprocess_proc.poll() is None:
                    self.subprocess_proc.kill()
            except Exception as e:
                print(f"终止子进程时出错: {e}")

        return True

    def wait_for_completion(self, check_interval=1.0):
        """等待训练完成"""
        try:
            while self.is_running:
                time.sleep(check_interval)

                # 检查是否有结果
                try:
                    result = self.result_queue.get_nowait()
                    status, message = result
                    print(f"训练结果: {status} - {message}")
                    return status, message
                except queue.Empty:
                    continue

        except KeyboardInterrupt:
            print("\n收到中断信号，正在停止训练...")
            self.stop_training()
            # 等待训练线程结束
            if self.training_thread and self.training_thread.is_alive():
                self.training_thread.join(timeout=10)
            return "interrupted", "训练被用户中断"

        return "unknown", "训练状态未知"


def setup_enhanced_parser():
    """设置增强的参数解析器"""
    parser = setup_parser()

    # 添加执行模式参数
    parser.add_argument(
        "--execution_mode",
        type=str,
        choices=["thread", "subprocess"],
        default="thread",
        help="执行模式: thread(线程模式) 或 subprocess(子进程模式)"
    )

    return parser


def signal_handler(signum, frame):
    """信号处理器"""
    print(f"\n收到信号 {signum}，正在停止训练...")
    if hasattr(signal_handler, 'executor') and signal_handler.executor:
        signal_handler.executor.stop_training()
    sys.exit(0)


def main():
    """主函数"""
    # 设置信号处理
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)

    # 解析参数
    parser = setup_enhanced_parser()
    args = parser.parse_args()

    # 显示配置信息
    print("=== LoRA 训练配置 ===")
    print(f"执行模式: {args.execution_mode}")
    print(f"预训练模型: {args.pretrained_model_name_or_path}")
    print(f"训练数据目录: {args.train_data_dir}")
    print(f"输出目录: {args.output_dir}")
    print(f"输出名称: {args.output_name}")
    print(f"网络维度: {args.network_dim}")
    print(f"学习率: {args.learning_rate}")
    print(f"最大训练步数: {args.max_train_steps}")
    print("=" * 30)

    # 创建训练执行器
    executor = TrainingExecutor(args.execution_mode)
    signal_handler.executor = executor  # 为信号处理器设置执行器引用

    # 启动训练
    if executor.start_training(args):
        # 等待训练完成
        status, message = executor.wait_for_completion()

        if status == "success":
            print("\n🎉 训练成功完成!")
            sys.exit(0)
        elif status == "error":
            print(f"\n❌ 训练失败: {message}")
            sys.exit(1)
        elif status in ["stopped", "interrupted"]:
            print(f"\n⏹️ 训练被中断: {message}")
            sys.exit(2)
        else:
            print(f"\n❓ 训练状态未知: {message}")
            sys.exit(3)
    else:
        print("❌ 启动训练失败")
        sys.exit(1)


if __name__ == "__main__":
    main()
