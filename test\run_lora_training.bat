@echo off
chcp 65001
setlocal enabledelayedexpansion

:: 设置Python路径
set PYTHON_PATH=E:\AI\python\python.exe

:: 设置脚本路径
set SCRIPT_PATH=%~dp0train_lora_node_subprocess.py
echo 正在调用脚本: %SCRIPT_PATH%

:: 参数设置
set MODEL_PATH=E:\AI\Models\model.safetensors
set DATA_DIR=E:\AI\TrainingData
set OUTPUT_DIR=E:\AI\LoraOutput
set OUTPUT_NAME=my_lora
set NETWORK_DIM=32
set NETWORK_ALPHA=32
set BATCH_SIZE=1
set LR=0.0001
set STEPS=10

:: 设置 PYTHONPATH
set PYTHONPATH=E:\AI\ComfyUI\custom_nodes\comfyui_lora_trainer;E:\AI\ComfyUI\custom_nodes\comfyui_lora_trainer\sd_scripts;%PYTHONPATH%
echo PYTHONPATH设置为: %PYTHONPATH%

:: 执行命令
%PYTHON_PATH% "%SCRIPT_PATH%" ^
    --pretrained_model_name_or_path "%MODEL_PATH%" ^
    --train_data_dir "%DATA_DIR%" ^
    --output_dir "%OUTPUT_DIR%" ^
    --output_name "%OUTPUT_NAME%" ^
    --network_dim %NETWORK_DIM% ^
    --network_alpha %NETWORK_ALPHA% ^
    --batch_size %BATCH_SIZE% ^
    --learning_rate %LR% ^
    --max_train_steps %STEPS% ^
    --save_model_as safetensors ^
    --mixed_precision fp16 ^
    --save_every_n_steps 500 ^
    --seed 42

if %ERRORLEVEL% neq 0 (
    echo 训练失败，错误代码: %ERRORLEVEL%
    pause
    exit /b %ERRORLEVEL%
)

echo 训练完成!
pause
