@echo off
set PYTHONPATH=%~dp0;%PYTHONPATH%

python plugins/kohya_ss/train_network.py ^
    --pretrained_model_name_or_path %1 ^
    --train_data_dir %2 ^
    --output_dir %3 ^
    --output_name %4 ^
    --network_dim %5 ^
    --network_alpha %6 ^
    --learning_rate %7 ^
    --max_train_epochs %8 ^
    --train_batch_size %9 ^
    --save_model_as safetensors ^
    --mixed_precision fp16 ^
    --network_module networks.lora ^
    --save_every_n_epochs 1 ^
    --save_every_n_steps 1000 ^
    --save_state true ^
    --resume false ^
    --lr_scheduler cosine_with_restarts ^
    --lr_warmup_steps 0 ^
    --lr_scheduler_num_cycles 1 ^
    --lr_scheduler_power 1 ^
    --max_token_length 75 ^
    --clip_skip 1 ^
    --seed 42 ^
    --network_train_unet_only false ^
    --network_train_text_encoder_only false ^
    --training_comment "Training with ComfyUI LoRA Trainer"

pause 