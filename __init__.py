"""
ComfyUI LoRA Trainer Plugin

A comprehensive LoRA training solution for ComfyUI with support for:
- SD 1.5 and SDXL models
- Multiple network types (LoRA, LoHa, LoKr, etc.)
- Advanced training configurations
- Real-time monitoring and logging
"""

import sys
from pathlib import Path
import os

# Plugin information

__version__ = "1.0.0"
__author__ = "ComfyUI LoRA Trainer Team"

# Setup paths
PLUGIN_ROOT = Path(__file__).parent

# Add plugin and sd_scripts to Python path
sys.path.append(str(PLUGIN_ROOT))
sys.path.append( str(PLUGIN_ROOT / "sd_scripts"))


for module in sys.modules:
    if "comfyui_lora_trainer" in module:
        print(f"    ================ module: {module}")

print("\n\n\n\n\n")
for p in sys.path:
    if "comfyui_lora_trainer" in p:
        print(f"    ================ sys.path: {p}")



# Import nodes
try:
    from .sd_scripts.library.train_util import verify_command_line_training_args, read_config_from_file
    from .train_lora_node import NODE_CLASS_MAPPINGS as LORA_NODES
    from .train_lora_node import NODE_DISPLAY_NAME_MAPPINGS as LORA_NAMES


    # Combine all node mappings
    NODE_CLASS_MAPPINGS = {
        **LORA_NODES,
    }

    NODE_DISPLAY_NAME_MAPPINGS = {
        **LORA_NAMES,

    }

except ImportError as e:
    import traceback
    print(f"Error: Failed to import LoRA trainer nodes: {e}")
    print("Full traceback:")
    traceback.print_exc()
    NODE_CLASS_MAPPINGS = {}
    NODE_DISPLAY_NAME_MAPPINGS = {}

# Export public interface
__all__ = [
    'NODE_CLASS_MAPPINGS',
    'NODE_DISPLAY_NAME_MAPPINGS',
]
