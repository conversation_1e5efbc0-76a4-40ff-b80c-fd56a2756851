#!/usr/bin/env python3
"""
测试训练脚本的线程和子进程模式
"""

import sys
import os
from pathlib import Path

# 添加当前目录到 sys.path
CURRENT = Path(__file__).resolve().parent
sys.path.insert(0, str(CURRENT))

from training import TrainingExecutor, setup_enhanced_parser


def create_test_args():
    """创建测试用的参数"""
    parser = setup_enhanced_parser()
    
    # 模拟命令行参数
    test_args = [
        "--execution_mode", "thread",
        "--pretrained_model_name_or_path", "test_model.safetensors",
        "--train_data_dir", "test_data",
        "--output_dir", "test_output",
        "--output_name", "test_lora",
        "--network_dim", "32",
        "--network_alpha", "32",
        "--train_batch_size", "1",
        "--learning_rate", "0.0001",
        "--max_train_steps", "10",  # 很少的步数用于测试
        "--save_model_as", "safetensors",
        "--mixed_precision", "fp16",
        "--save_every_n_steps", "5",
        "--seed", "42",
        "--resolution", "512,512",
        "--network_module", "networks.lora"
    ]
    
    return parser.parse_args(test_args)


def test_thread_mode():
    """测试线程模式"""
    print("=== 测试线程模式 ===")
    
    args = create_test_args()
    args.execution_mode = "thread"
    
    executor = TrainingExecutor("thread")
    
    print("创建线程模式执行器成功")
    print(f"执行模式: {executor.execution_mode}")
    print(f"运行状态: {executor.is_running}")
    
    # 注意：这里不实际启动训练，只测试执行器的创建和配置
    print("线程模式测试完成")
    return True


def test_subprocess_mode():
    """测试子进程模式"""
    print("\n=== 测试子进程模式 ===")
    
    args = create_test_args()
    args.execution_mode = "subprocess"
    
    executor = TrainingExecutor("subprocess")
    
    print("创建子进程模式执行器成功")
    print(f"执行模式: {executor.execution_mode}")
    print(f"运行状态: {executor.is_running}")
    
    # 注意：这里不实际启动训练，只测试执行器的创建和配置
    print("子进程模式测试完成")
    return True


def test_parameter_parsing():
    """测试参数解析"""
    print("\n=== 测试参数解析 ===")
    
    args = create_test_args()
    
    print(f"执行模式: {args.execution_mode}")
    print(f"预训练模型: {args.pretrained_model_name_or_path}")
    print(f"训练数据目录: {args.train_data_dir}")
    print(f"输出目录: {args.output_dir}")
    print(f"网络维度: {args.network_dim}")
    print(f"学习率: {args.learning_rate}")
    print(f"最大训练步数: {args.max_train_steps}")
    
    print("参数解析测试完成")
    return True


def main():
    """主测试函数"""
    print("开始测试训练脚本的线程和子进程模式...")
    print("=" * 50)
    
    try:
        # 测试参数解析
        if not test_parameter_parsing():
            print("❌ 参数解析测试失败")
            return False
            
        # 测试线程模式
        if not test_thread_mode():
            print("❌ 线程模式测试失败")
            return False
            
        # 测试子进程模式
        if not test_subprocess_mode():
            print("❌ 子进程模式测试失败")
            return False
            
        print("\n" + "=" * 50)
        print("🎉 所有测试通过!")
        print("\n使用说明:")
        print("1. 线程模式 (默认): 在当前进程中使用线程执行训练")
        print("   - 优点: 资源共享，启动快速")
        print("   - 缺点: 可能受到主进程影响")
        print("   - 使用: --execution_mode thread")
        print()
        print("2. 子进程模式: 创建独立子进程执行训练")
        print("   - 优点: 资源隔离，更稳定，可独立中断")
        print("   - 缺点: 启动稍慢，资源开销稍大")
        print("   - 使用: --execution_mode subprocess")
        print()
        print("修改 training.bat 中的 EXECUTION_MODE 变量来切换模式")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试过程中出错: {e}")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
