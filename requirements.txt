# ComfyUI LoRA Trainer Requirements
# Core dependencies for LoRA training functionality

# Basic dependencies (usually already available in ComfyUI environment)
torch>=2.0.0
torchvision>=0.15.0
Pillow>=9.0.0
numpy>=1.21.0
tqdm>=4.65.0
PyYAML>=6.0

# Training dependencies
accelerate>=0.25.0
transformers>=4.35.0
diffusers>=0.24.0
safetensors>=0.4.0
huggingface-hub>=0.20.0

# Optimization dependencies
bitsandbytes>=0.41.0
xformers>=0.0.20; sys_platform != "darwin"

# Optional optimizers
prodigyopt>=1.0
lion-pytorch>=0.0.6

# Utilities
ftfy>=6.1.0
einops>=0.7.0
opencv-python>=4.8.0
imagesize>=1.4.0
rich>=13.0.0

# Logging and monitoring
tensorboard>=2.10.0

# Configuration
toml>=0.10.0
voluptuous>=0.13.0

# Note: Some dependencies may already be installed with ComfyUI
# If you encounter conflicts, try installing without version constraints:
# pip install -r requirements.txt --no-deps